import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CommonService } from './common.service';
import { StorageService } from './storage.service';
import { lastValueFrom } from 'rxjs';
import { Order, CreateOrderRequest, OrderItem } from '../models';
import { TypeSenseService } from './typesense.service';
@Injectable({
    providedIn: 'root'
})
export class OrderService {
    constructor(
        private commonService: CommonService,
        private storageService: StorageService,
        private typesense: TypeSenseService
    ) { }
    
    async createOrder(data: { customerName: string, customerPhone: string, total: number, items: Array<{ sku: string, unit_price: number, sale_price: number, quantity: number }> }): Promise<Order> {
        const user: any = this.storageService.getItem('user');
        const totalAmount = data.total;
        const orderData: CreateOrderRequest = {
        customer_id: user?.uid,
        customer_name: data.customerName || 'Unknown',
        facility_id: this.commonService.currentFacility?.facilityId || '',
        facility_name: this.commonService.currentFacility?.facilityCode || '',
        total_amount: totalAmount,
        payment: {
            payment_mode : "cash",
            create_payment_order: false
        },
        is_approved: false,
        address: {
          full_name: data.customerName || 'Unknown',
          phone_number: data.customerPhone,
          address_line1: '123 Main Street',
          address_line2: 'Apt 4B',
          city: 'Bangalore',
          state: 'Karnataka',
          postal_code: '56001',
          country: 'INDIA',
          type_of_address: 'work',
        },
        items: data.items,
      };
      return await lastValueFrom(this.commonService.post('v1/create_order', orderData));
    }

    async getOrders(): Promise<Order[]> {
        const params = {
            facility_name: this.commonService.currentFacility?.facilityCode
        }
        const data: any = await lastValueFrom(this.commonService.get<Order[]>('v1/orders', params));
        return data;
    }

    async getOrderDetails(id: string): Promise<Order> {
        const data: any = await lastValueFrom(this.commonService.get<Order>(`v1/order_details`, {order_id: id}));
        if (data.success){
            const sku = data.data.items.map((el: any) => el.sku)
           if (sku?.length){
            const product = await this.typesense.getProductBySku(sku, sku.length);
            if (product?.length){
                const skus: any = product.reduce((acc: any, el: any) => ({...acc, [el.child_sku]: el}), {})
                data.data.items?.forEach((el: any, index: number) => {
                    data.data.items[index] = {...el, ...skus[el.sku]};
                })
            }
           }
        }
        return data;
    }
}