import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TaxConfig } from '../models/print.model';
import { StorageService } from './storage.service';


@Injectable({
  providedIn: 'root'
})
export class TaxConfigService {
  private readonly DEFAULT_TAX_CONFIG: TaxConfig = {
    isInclusive: true,
    splitIgst: true,
  };
  private readonly TAX_CONFIG_STORAGE_KEY = 'tax_config';
  
  private taxConfigSubject: BehaviorSubject<TaxConfig>;
  
  constructor(private storageService: StorageService) {
    const storedConfig = this.storageService.getItem(this.TAX_CONFIG_STORAGE_KEY) as string;
    const initialConfig = storedConfig ? JSON.parse(storedConfig) : this.DEFAULT_TAX_CONFIG;
    this.taxConfigSubject = new BehaviorSubject<TaxConfig>(initialConfig);
  }

  getTaxConfig(): Observable<TaxConfig> {
    return this.taxConfigSubject.asObservable();
  }

  
  getTaxConfigValue(): TaxConfig {
    return this.taxConfigSubject.getValue();
  }

  updateTaxConfig(config: Partial<TaxConfig>): void {
    const currentConfig = this.taxConfigSubject.getValue();
    const newConfig = { ...currentConfig, ...config };
    this.storageService.setItem(this.TAX_CONFIG_STORAGE_KEY, JSON.stringify(newConfig));
    this.taxConfigSubject.next(newConfig);
  }

  resetToDefaults(): void {
    this.updateTaxConfig(this.DEFAULT_TAX_CONFIG);
  }
}
