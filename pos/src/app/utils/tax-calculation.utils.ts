import { CartItem, PrintableItem, PrintTotals, TaxConfig } from '../models';

export class TaxCalculationUtils {
  private static defaultTaxConfig: TaxConfig = {
    isInclusive: true,
    splitIgst: true,
  };


  private static taxConfig: TaxConfig = { ...TaxCalculationUtils.defaultTaxConfig };

  static setTaxConfig(config: Partial<TaxConfig>): void {
    this.taxConfig = { ...this.defaultTaxConfig, ...config };
  }

  static getTaxConfig(): TaxConfig {
    return { ...this.taxConfig };
  }

  static calculateTaxableAmount(
    salePrice: number,
    taxRates: { igst?: number; cess?: number },
    quantity: number
  ): number {
    const totalTaxRate = (taxRates.igst || 0) + (taxRates.cess || 0);

    if (this.taxConfig.isInclusive && totalTaxRate > 0) {
      return (salePrice * quantity) / (1 + (totalTaxRate / 100));
    }
    return salePrice * quantity;
  }

  static calculateTaxAmounts(
    taxableAmount: number,
    taxRates: { igst?: number; cess?: number }
  ): {
    igstAmount: number;
    cgstAmount: number;
    sgstAmount: number;
    cessAmount: number;
    totalTaxAmount: number;
  } {
    const igstAmount = taxRates.igst ? (taxableAmount * taxRates.igst) / 100 : 0;
    const cessAmount = taxRates.cess ? (taxableAmount * taxRates.cess) / 100 : 0;

    const cgstAmount = this.taxConfig.splitIgst ? igstAmount / 2 : 0;
    const sgstAmount = this.taxConfig.splitIgst ? igstAmount / 2 : 0;

    const totalTaxAmount = igstAmount + cessAmount;

    return {
      igstAmount,
      cgstAmount,
      sgstAmount,
      cessAmount,
      totalTaxAmount
    };
  }

  static calculateItemTaxDetails(item: PrintableItem): PrintableItem {
    const taxRates = {
      igst: item.igst || 0,
      cess: item.cess || 0
    };

    const taxableAmount = this.calculateTaxableAmount(
      item.sale_price,
      taxRates,
      item.quantity
    );

    const {
      igstAmount,
      cgstAmount,
      sgstAmount,
      cessAmount,
      totalTaxAmount
    } = this.calculateTaxAmounts(taxableAmount, taxRates);

    const totalWithTax = this.taxConfig.isInclusive ? item.sale_price * item.quantity : taxableAmount + totalTaxAmount;

    return {
      ...item,
      taxableAmount,
      igstAmount,
      cgstAmount,
      sgstAmount,
      cessAmount,
      totalTax: totalTaxAmount,
      totalWithTax
    };
  }

  static calculatePrintableTotals(items: PrintableItem[]): PrintTotals {
    let totalTaxableAmount = 0;
    let totalIgstAmount = 0;
    let totalCgstAmount = 0;
    let totalSgstAmount = 0;
    let totalCessAmount = 0;
    let totalQuantity = 0;

    const processedItems = items.map(item => {
      const processedItem = this.calculateItemTaxDetails(item);

      totalTaxableAmount += processedItem.taxableAmount || 0;
      totalIgstAmount += processedItem.igstAmount || 0;
      totalCgstAmount += processedItem.cgstAmount || 0;
      totalSgstAmount += processedItem.sgstAmount || 0;
      totalCessAmount += processedItem.cessAmount || 0;
      totalQuantity += processedItem.quantity;

      return processedItem;
    });

    const totalTaxAmount = totalIgstAmount + totalCessAmount;

    return {
      totalTaxableAmount,
      totalCgstAmount,
      totalSgstAmount,
      totalIgstAmount,
      totalCessAmount,
      totalQuantity,
      totalTaxAmount,
      processedItems
    };
  }

  static convertCartItemToPrintableItem(item: CartItem): PrintableItem {
    const sku = item.child_sku || '';
    const salePrice = item.selling_price;
    const mrp = item.mrp || salePrice;
    const igstRate = item.igst || 0;
    const cessRate = item.cess || 0;

    return {
      sku,
      name: item.name,
      quantity: item.quantity,
      unit_price: mrp,
      sale_price: salePrice,
      mrp,
      discount: Math.max(0, mrp - salePrice),
      total: salePrice * item.quantity,
      cgst: item.cgst || 0,
      sgst: item.sgst || 0,
      igst: igstRate,
      cess: cessRate
    };
  }
}
