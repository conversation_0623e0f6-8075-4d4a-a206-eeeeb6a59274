import { CartItem, PrintableItem, PrintTotals } from '../models';
import { TaxCalculationUtils } from './tax-calculation.utils';

export class CartCalculationUtils {

  static calculateSubTotal(cartItems: CartItem[]): number {
    return cartItems.reduce(
      (total, item) => {
        const price = item.mrp || item.selling_price;
        return total + price * item.quantity;
      },
      0,
    );
  }


  static calculateDiscount(cartItems: CartItem[]): number {
    return cartItems.reduce(
      (total, item) => {
        const mrp = item.mrp || 0;
        const sellingPrice = item.selling_price || 0;
        const discountPerUnit = Math.max(0, mrp - sellingPrice);
        return total + (discountPerUnit * item.quantity);
      },
      0,
    );
  }


  static calculateGrandTotal(cartItems: CartItem[]): number {
    return this.calculateSubTotal(cartItems) - this.calculateDiscount(cartItems);
  }


  static calculateItemDiscount(item: CartItem): number {
    const mrp = item.mrp || 0;
    const sellingPrice = item.selling_price || 0;
    const discountPerUnit = Math.max(0, mrp - sellingPrice);
    return discountPerUnit * item.quantity;
  }


  static calculateItemTotal(item: CartItem): number {
    if (item.mrp && item.selling_price) {
      const price = item.mrp || item.selling_price;
      const itemSubtotal = price * item.quantity;
      const itemDiscount = this.calculateItemDiscount(item);
      return itemSubtotal - itemDiscount;
    }
    return item.selling_price * item.quantity;
  }

  static formatCurrency(amount: number): string {
    if (amount) {
      return `₹${amount.toFixed(2)}`;
    }
    return '₹0.00';
  }


  static calculatePrintableTotals(items: PrintableItem[]): PrintTotals {
    const result = TaxCalculationUtils.calculatePrintableTotals(items);

    const backwardCompatibleResult = {
      ...result,
      totalTax: result.totalTaxAmount,
      totalGstAmount: result.totalGstAmount || result.totalTaxAmount
    };

    return backwardCompatibleResult;
  }
}