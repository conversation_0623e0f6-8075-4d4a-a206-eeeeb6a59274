import { ChangeDetectorRef, Component } from "@angular/core";
import { OrderService } from "src/app/services/order.service";
import { PrintService } from "src/app/services/print.service";
import { CommonService } from "src/app/services/common.service";
import { SharedModule } from "src/app/shared.module";
@Component({
  selector: 'app-invoices',
  standalone: true,
  templateUrl: './invoices.html',
  imports: [
    SharedModule
  ]
})
export class InvoicesComponent {
  invoices: any[] = [];
  invoicesColumns: any[] = [];
  cartItems: any[] = [];
  isLoading = false;
  ordersData: any = null;
  selectedInvoice: any = null;

  constructor(
    private orderService: OrderService,
    private commonService: CommonService,
    private cdr: ChangeDetectorRef,
    private printService: PrintService
  ) {
  }
  ionViewDidEnter() {
    this.isLoading = true;
    this.selectedInvoice = null;
    this.invoices = [];
    this.cartItems = [];
    this.invoicesColumns = [
      { field: 'order_id', header: 'Order ID' },
      { field: 'customer_name', header: 'Customer' },
      { field: 'created_at', header: 'Date', body: (data: any) => new Date(data.created_at).toLocaleString() },
      { field: 'status', header: 'Status' },
      { field: 'total_amount', header: 'Total' },
    ];
    this.loadOrders();
  }
  loadOrders() {
    this.isLoading = true;
    this.orderService.getOrders().then((orders: any) => {
      this.invoices = orders?.data;
      this.isLoading = false;
    }).catch((error: any) => {
      this.isLoading = false;
      console.error('Error fetching orders:', error);
      this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to load orders. Please try again.' });
    });
  }
  onRowClick(ev: any) {
    this.selectedInvoice = ev.rowData;
    this.getRowData(ev.rowData?.order_id)
  }
  async getRowData(id: string) {
    const data: any = await this.orderService.getOrderDetails(id)
    this.ordersData = data?.data || null;
    this.cartItems = data?.data?.items || [];
    this.cdr.detectChanges()
  }
  invoiceGrandTotal(): number {
    return this.cartItems.reduce((total, item) => total + (item.selling_price || 0) * (item.quantity || 0), 0);
  }

  printInvoice() {
    if (!this.ordersData || this.cartItems.length === 0) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Please select an order to print invoice copy'
      });
      return;
    }
    this.printService.printOrder(this.ordersData, true);
  }
  clearCart() {
    this.cartItems = [];
    this.ordersData = null;
    this.selectedInvoice = null;
    this.cdr.detectChanges();
  }
}