<ion-content [fullscreen]="true">
  <div class="w-full bg-blue-50 p-3 rounded-md mb-3 lg:hidden" *ngIf="cartItems.length > 0">
    <div class="flex items-center justify-center gap-2">
      <span class="font-semibold">Invoice Total:</span>
      <span class="text-lg font-bold">₹{{invoiceGrandTotal().toFixed(2)}}</span>
    </div>
  </div>
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 p-3">
    <div class="col-span-1 lg:col-span-2 mt-3">
      <app-table [loading]="isLoading" [tableData]="invoices" [tableColumns]="invoicesColumns" [dataKey]="'order_id'"
        [selectedRows]="selectedInvoice ? [selectedInvoice] : []" (onRowClick)="onRowClick($event)">
      </app-table>
    </div>
    <div class="col-span-1 lg:col-span-1 p-3">
      <app-billing [noDataTitle]="''" [noDataMessage]="'Select Order to view details'" [showTable]="true"
        [cartItems]="cartItems || []"></app-billing>
      <button *ngIf="cartItems.length > 0" class="w-full mt-3 text-xl" pButton icon="pi pi-print"
        severity="info" outlined="true" label="Print Invoice" (click)="printInvoice()"></button>
    </div>
  </div>
</ion-content>