import { Component } from "@angular/core";
import { IonHeader, IonToolbar, IonButtons, IonMenuButton } from "@ionic/angular/standalone";
import { DropdownModule } from "primeng/dropdown";
import { Facility } from "src/app/models";
import { FormsModule } from "@angular/forms";
import { CommonService } from "src/app/services/common.service";
import { FirestoreService } from "src/app/firebase/firestore.service";
import { Subject, from } from "rxjs";
import { StorageService } from "src/app/services/storage.service";
import { FacilityStoreService } from "src/app/firebase/facility-store.service";
@Component({
    selector: 'app-header',
    standalone: true,
    templateUrl: './header.html',
    imports: [ IonButtons, IonToolbar, IonHeader, IonMenuButton, DropdownModule, FormsModule]
})
export class HeaderComponent {
  facilities : Facility[] = [];
  facility: any = {};
  private destroy$ = new Subject<void>();
  constructor(
    private commonService: CommonService,
    private firestoreService: FirestoreService,
    private storage: StorageService,
    private facilityStoreService: FacilityStoreService
  ) {
   this.getFacilities();
  }
  ionViewDidEnter() {
    this.facilities = [];
    this.getFacilities();
  }
  async getFacilities(){
    const facilities = this.storage.getItem('facilities');
    if (facilities) {
      this.prepareFacilities(facilities as Facility[]);
    } else {
      const userFields = await this.firestoreService.getCurrentUserFields();
      if (userFields) {
        const assignedFacilities = [];
        const facilityIds = typeof userFields.facilities === 'string' ? [userFields.facilities] : userFields.facilities;
        for (const facilityId of facilityIds) {
          const facility = await this.facilityStoreService.getFacilityById(facilityId);
          if (facility) {
            assignedFacilities.push(facility);
          }
        }
        this.prepareFacilities(assignedFacilities);
      }
    }
  }
  async prepareFacilities(facilities : Facility[]){
    this.facilities = facilities;
    this.storage.setItem('facilities', facilities);
    const currentFacility = this.storage.getItem('currentFacility');
    if (currentFacility) {
      this.facility = this.facilities.find(f => f.facilityId === currentFacility as string);
    } else {
      this.facility = this.facilities[0];
    }
    this.commonService.currentFacility = this.facility;
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  async facilityChange(event: any){
    const selectedFacility = event.value;
    this.commonService.currentFacility = selectedFacility;
    this.storage.setItem('currentFacility', selectedFacility.facilityId as string);
  }
}