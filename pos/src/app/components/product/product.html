<div class="w-full cursor-pointer {{bgColor}} p-2 rounded-lg">
    <div class="w-full h-full flex items-center">
        <p-image preview="true" imageClass="rounded-md" (onImageError)="onImageError($event)" [imageStyle]="{minWidth: imageWidth, maxWidth: imageWidth, minHeight: imageHeight, maxHeight: imageHeight, objectFit: 'cover'}" [src]="product.thumbnail_image" alt="" />
        <div class="w-full ml-3">
            <p class="text-sm m-0 leading-tight mb-2 line-clamp-2 ">
                {{product.name}}
            </p>
            <p *ngIf="showSku" class="text-xs text-gray-500 leading-1" style="margin: 0 !important;">
                SKU: {{product.child_sku}}
            </p>
            <p *ngIf="showPrice" class="text-xs mt-2 text-gray-500 m-0 leading-1" style="margin: 0 !important;">
                <span class="line-through text-gray-500 font-semibold">₹ {{product.mrp?.toFixed(2)}}</span>
                <span class="ml-2 text-orange-500 font-semibold">₹ {{product.selling_price?.toFixed(2)}}</span>
            </p>
        </div>
    </div>
</div>