import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { CommonService } from '../../services/common.service';
import { StorageService } from '../../services/storage.service';
import { OrderService } from '../../services/order.service';
import { PrintService } from '../../services/print.service';
import { CartItem, OrderItem } from '../../models';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-billing',
  standalone: false,
  templateUrl: './billing.html',
})
export class BillingComponent implements OnChanges {
  showConfirmDialog = false;
  @Input() showTable = false;
  @Input() cartItems: CartItem[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() cartChange = new EventEmitter<CartItem[]>();
  @Input() cartColumns = [
    { field: 'thumbnail_image', header: 'Image', type: 'image' },
    { field: 'name', header: 'SKU' },
    { field: 'selling_price', header: 'Price' },
    { field: 'quantity', header: 'Quantity'}
  ]
  @Input() noDataTitle = 'Add items to cart';
  @Input() noDataMessage = '';
  isProcessingCheckout = false;
  showPaymentModal = false;
  currentCustomerName = '';

  constructor(
    private commonService: CommonService,
    private cdr: ChangeDetectorRef,
    private storageService: StorageService,
    private orderService: OrderService,
    private printService: PrintService,
    private messageService: MessageService
  ) { }

  ngOnChanges(_changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  removeFromCart(product: CartItem) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
      this.cartChange.emit([...this.cartItems]);
    }
  }

  updateQuantity(event: { value: number }, product: CartItem) {
    const newQuantity = event.value;
    if (newQuantity < 1) {
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
      this.cartChange.emit([...this.cartItems]);
    }
  }

  getSubTotal() {
    return this.cartItems.reduce((total, item) => total + (item.selling_price || 0) * item.quantity, 0);
  }

  getTaxes() {
    return this.cartItems.reduce((total, item) => {
      if (item.taxable) {
        return total + (item.tax / 100) * (item.selling_price || 0) * item.quantity;
      }
      return total;
    }, 0);
  }

  getGrandTotal() {
    return this.getSubTotal() + this.getTaxes();
  }

  clearCart() {
    this.cartItems.length = 0;
    this.currentCustomerName = '';
    this.cartChange.emit([...this.cartItems]);
    this.cdr.detectChanges();
  }

  confirmCheckout() {
    this.showConfirmDialog = true;
  }

  onPaymentCancel() {
    this.showConfirmDialog = false;
  }

  createOrder(event: any) {
    try {
      // Determine payment method and details
      let paymentMethod = event.paymentMethod;
      let paymentDetails = '';

      if (event.multiPaymentData) {
        // Mixed payment
        const { cashAmount, onlineAmount, onlineMethod, upiId, cardType } = event.multiPaymentData;
        paymentMethod = 'mixed';
        paymentDetails = `Cash: ₹${cashAmount.toFixed(2)}, ${onlineMethod.toUpperCase()}: ₹${onlineAmount.toFixed(2)}`;
        if (onlineMethod === 'upi' && upiId) {
          paymentDetails += ` (${upiId})`;
        } else if (onlineMethod === 'card' && cardType) {
          paymentDetails += ` (${cardType})`;
        }
      } else {
        // Single payment method
        const amountKey = `${event.paymentMethod}Amount`;
        const amount = event.paymentData[amountKey];
        paymentDetails = `₹${amount}`;

        if (event.paymentMethod === 'upi' && event.paymentData.upiId) {
          paymentDetails += ` (${event.paymentData.upiId})`;
        } else if (event.paymentMethod === 'card' && event.paymentData.cardType) {
          paymentDetails += ` (${event.paymentData.cardType})`;
        }
      }

      const data = {
        customerName: event.customerName,
        customerPhone: event.customerPhone,
        total: this.getGrandTotal(),
        paymentMethod: paymentMethod,
        paymentDetails: paymentDetails,
        items: this.cartItems.map((item): Omit<OrderItem, 'total_price'> => ({
          sku: item.child_sku,
          unit_price: item.selling_price,
          sale_price: item.selling_price,
          quantity: item.quantity,
          tax: item.tax,
        })),
      }

      // Store customer name and payment info for printing
      this.currentCustomerName = event.customerName || 'Walk-in Customer';
      this.isProcessingCheckout = true;
      this.orderService.createOrder(data).then((order: any) => {
        this.commonService.toast({
          severity: 'success',
          summary: 'Success',
          detail: `Order created successfully! Total: ₹${this.getGrandTotal().toFixed(2)} (${paymentMethod === 'mixed' ? 'Mixed Payment' : paymentMethod.toUpperCase()})`
        });
        if (order?.data?.order_id) {
          this.printReceipt(order.data.order_id, paymentMethod, paymentDetails);
        }
        this.clearCart();
        this.showConfirmDialog = false;
        this.isProcessingCheckout = false;
      }).catch((error: any) => {
        let errorMessage = 'Order creation failed. Please try again.';
        if (error.status === 401) {
          errorMessage = 'Authentication failed. Please check your token.';
        } else if (error.status === 400) {
          errorMessage = 'Invalid order data. Please check the items.';
        }
        this.commonService.toast({ severity: 'error', summary: 'Error', detail: errorMessage });
        this.isProcessingCheckout = false;
      })
    } catch (error) {
      this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to create order. Please try again.' });
      this.isProcessingCheckout = false;
    }
  }

  onConfirmCheckout(): void {
    if (this.cartItems.length === 0) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Empty Cart',
        detail: 'Please add items to cart before checkout'
      });
      return;
    }
    this.showPaymentModal = true;
    console.log('Payment modal should be visible now'); // Debug log
  }

  onPaymentConfirm(ev: any) {
    this.createOrder(ev);
    this.showPaymentModal = false;
  }

  onCancelCheckout() {
    this.currentCustomerName = '';
    this.showPaymentModal = false;
  }

  printReceipt(orderId?: string, paymentMethod?: string, paymentDetails?: string): void {
    if (this.cartItems.length === 0) {
      this.commonService.toast({ severity: 'warn', summary: 'Warning', detail: 'No items in cart to print' });
      return;
    }
    const customerName = this.currentCustomerName || 'Walk-in Customer';
    const orderIdToPrint = orderId || `TEMP-${Date.now()}`;
    const paymentMethodForPrint = paymentMethod || 'Cash';

    const printData = {
      cartItems: this.cartItems,
      orderId: orderIdToPrint,
      customerName,
      paymentMethod: paymentMethodForPrint,
      paymentDetails: paymentDetails,
      copyOfInvoice: false
    }
    this.printService.printCart(printData);
  }

  onAddUser(): void {
    // Here you can implement the logic to add a new user
    this.messageService.add({
      severity: 'info',
      summary: 'Add User',
      detail: 'Opening user creation dialog...'
    });
  }
}
