<p-dialog appendTo="body" [modal]="true" [(visible)]="visible" [style]="{width: '50vw', 'max-width':'800px'}"
  [breakpoints]="{'960px': '75vw', '640px': '95vw'}" [closable]="false" [draggable]="false" [resizable]="false">

  <ng-template pTemplate="header">
    <div class="flex justify-between w-full">
      <h5>{{title}}</h5>
    </div>
  </ng-template>
  <div class="flex flex-col text-md">
    <!-- Customer Information -->
    <div class="w-full mb-2 mt-2">
      <h6 class="mb-2 leading-none font-semibold text-gray-800">Customer Information</h6>
      <div class="flex gap-4 mt-3">
        <input pInputText id="customerName" placeholder="Customer Name" [(ngModel)]="customerName" [disabled]="isProcessing" autocomplete="off" class="w-full cutom-input" />
        <input pInputText id="customerPhone" placeholder="Phone Number" [(ngModel)]="customerPhone" [disabled]="isProcessing" autocomplete="off" class="w-full cutom-input" />
      </div>
    </div>
    <!-- Payment Methods -->
    <div class="w-full mt-2">
      <h6 class="mb-3 leading-none font-semibold text-gray-800">Select Payment Method</h6>
      <div class="grid grid-cols-2 gap-3 mb-6 md:grid-cols-3">
        <div *ngFor="let method of paymentMethods; trackBy: trackByMethod"
          (click)="handlePaymentMethodChange(method.value)"
          class="p-3 transition-all duration-200 border-2 rounded-lg cursor-pointer hover:shadow-md" [class]="paymentData.selectedPaymentMethod === method.value ?
               'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
          [class.opacity-60]="isProcessing">
          <div class="flex align-center items-center text-xl justify-center">
            <i [class]="method.icon + ' text-lg mr-2'" [ngClass]="paymentData.selectedPaymentMethod === method.value ? 'text-blue-600' : 'text-gray-600'"></i>
            <span class="text-sm font-medium text-center" [ngClass]="paymentData.selectedPaymentMethod === method.value ? 'text-blue-700' : 'text-gray-700'">
              {{method.label}}
            </span>
          </div>
        </div>
      </div>

      <!-- Payment Details -->
      <div class="mb-6" *ngIf="getSelectedMethod() as method">
        <!-- Cash Payment Completed - Show Online Payment Step -->
        <div *ngIf="showOnlinePaymentStep" class="mb-4 p-3 flex  gap-2 items-center justify-between border rounded-lg bg-green-50">
          <div class="flex items-center gap-3 mb-2">
            <i class="pi pi-check-circle text-green-600"></i>
            <h6 class="font-semibold leading-none text-green-800">Cash Payment Completed</h6>
            <p class="text-sm text-indigo-700 font-semibold">Cash received: ₹{{cashAmountPaid.toFixed(2)}}</p>
          </div>
          <!-- <p class="text-sm text-green-700">Remaining amount: ₹{{remainingAmount.toFixed(2)}}</p> -->

          <div class=" flex gap-2">
            <button pButton pRipple type="button" label="Pay via UPI"
              [class]="paymentData.selectedPaymentMethod === 'upi' ? 'p-button-success' : 'p-button-outlined'"
              (click)="onSwitchToUPI()"
              [disabled]="isProcessing" size="small"></button>
            <button pButton pRipple type="button" label="Pay via Card"
              [class]="paymentData.selectedPaymentMethod === 'card' ? 'p-button-success' : 'p-button-outlined'"
              (click)="onSwitchToCard()"
              [disabled]="isProcessing" size="small"></button>
          </div>
        </div>

        <!-- Regular Payment UI -->
        <div class="p-3 border rounded-lg">
          <div class="flex items-center gap-3 mb-4">
            <i [class]="method.icon + ' ' "></i>
            <h6 class="font-semibold leading-none">
              {{method.label}} Payment
              <span *ngIf="showOnlinePaymentStep" class="text-sm font-normal text-gray-600">
                (for remaining ₹{{remainingAmount.toFixed(2)}})
              </span>
            </h6>
          </div>

          <div class="space-y-4">
            <div *ngFor="let field of method.fields" class="mb-3">
              <!-- Amount Field -->
              <ng-container *ngIf="field.type === 'amount'">
                <label class="block mb-1 text-sm font-medium text-gray-700">{{field.label}}</label>
                <input placeholder="Enter amount" pInputText #amountInput type="number" [value]="paymentData[field.key] || ''"
                  (input)="onAmountInput(amountInput, field.key)" [min]="field.min" [max]="field.max"
                  [disabled]="isProcessing || (showOnlinePaymentStep && field.key.includes('Amount'))"
                  class="w-full p-2 border rounded" step="0.01" autocomplete="off">
              </ng-container>

              <!-- Text Field -->
              <ng-container *ngIf="field.type === 'text'">
                <label class="block mb-1 text-sm font-medium text-gray-700">{{field.label}}</label>
                <input type="text" pInputText [placeholder]="field.placeholder || ''"
                  [(ngModel)]="paymentData[field.key]" [disabled]="isProcessing" class="w-full"
                  [ngClass]="{'p-invalid': field.required && !paymentData[field.key]}">
              </ng-container>

              <!-- Radio Field -->
              <ng-container *ngIf="field.type === 'radio'">
                <label class="block mb-2 text-sm font-medium text-gray-700">{{field.label}}</label>
                <div class="flex items-center gap-6">
                  <div *ngFor="let option of field.options" class="flex items-center gap-2">
                    <p-radioButton [name]="field.key" [value]="option.value" [(ngModel)]="paymentData[field.key]"
                      [disabled]="isProcessing" [inputId]="field.key + option.value"></p-radioButton>
                    <label [for]="field.key + option.value" class="cursor-pointer">{{option.label}}</label>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Summary -->
    <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
      <div class="grid grid-cols-1 gap-2 text-2xl">
        <div class="flex justify-between">
          <span class="font-bold text-gray-800">Total Amount:</span>
          <span class="font-bold">{{ totalAmount | currency:'INR' }}</span>
        </div>

        <!-- Mixed Payment Breakdown -->
        <div *ngIf="cashPaymentCompleted" class="text-lg text-gray-600 border-t pt-2 mt-2">
          <div class="flex justify-between">
            <span>Cash Payment:</span>
            <span>{{ cashAmountPaid | currency:'INR' }}</span>
          </div>
          <div class="flex justify-between" *ngIf="showOnlinePaymentStep">
            <span>{{paymentData.selectedPaymentMethod === 'upi' ? 'UPI' : 'Card'}} Payment:</span>
            <span>{{ (totalAmount - cashAmountPaid) | currency:'INR' }}</span>
          </div>
        </div>

        <div class="flex justify-between text-lg text-gray-600 " *ngIf='remainingAmount>0 && !showOnlinePaymentStep'>
          <span>Remaining Amount:</span>
          <span>{{ remainingAmount | currency:'INR' }}</span>
        </div>
        <div *ngIf="change > 0" class="flex justify-between text-lg text-green-600">
          <span> Return Amount:</span>
          <span>{{ change | currency:'INR' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Buttons -->
  <ng-template pTemplate="footer">
    <div class="flex justify-end w-full gap-2 mt-2">
      <button pButton pRipple [severity]="'danger'" type="button" [label]="cancelButtonLabel" [disabled]="isProcessing" (click)="onCancel()"></button>
      <button pButton pRipple type="button"
        [label]="getConfirmButtonLabel()"
        [disabled]="isProcessing || (showOnlinePaymentStep && remainingAmount > 0)"
        [loading]="isProcessing"
        (click)="onConfirm()"></button>
    </div>
  </ng-template>
</p-dialog>