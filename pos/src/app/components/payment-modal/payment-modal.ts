import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, OnChanges } from '@angular/core';

import { PaymentService, PaymentMethod, PaymentData, PaymentModalOutput, MultiPaymentData } from '../../services/payment.config.service';

@Component({
  selector: 'app-payment-modal',
  standalone: false,
  templateUrl: './payment-modal.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentModalComponent implements OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Complete Payment';
  @Input() confirmButtonLabel = 'Confirm Payment';
  @Input() cancelButtonLabel = 'Cancel';

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<PaymentModalOutput>();

  paymentData: PaymentData = { selectedPaymentMethod: 'cash' };
  remainingAmount = 0;
  change = 0;
  customerName = '';
  customerPhone = '';
  paymentMethods: PaymentMethod[] = [];

  // Mixed payment specific properties
  isMixedPayment = false;
  cashAmountForMixed = 0;
  onlineAmountForMixed = 0;
  selectedOnlineMethod: 'upi' | 'card' = 'upi';

  constructor(private paymentService: PaymentService) {}

  ngOnChanges(): void {
    if (this.totalAmount !== undefined) {
      this.updateAmounts();
      this.paymentMethods = this.paymentService.getPaymentMethods(this.totalAmount);
    }
    if (this.visible) this.resetPaymentData();
  }

  trackByMethod(_: number, m: PaymentMethod): string { return m.value; }

  getSelectedMethod(): PaymentMethod | undefined {
    return this.paymentMethods.find(m => m.value === this.paymentData.selectedPaymentMethod);
  }

  private resetPaymentData(): void {
    this.paymentData = this.paymentService.initializePaymentData(this.totalAmount);
    this.customerName = '';
    this.customerPhone = '';
    this.isMixedPayment = false;
    this.cashAmountForMixed = 0;
    this.onlineAmountForMixed = 0;
    this.selectedOnlineMethod = 'upi';
    this.updateAmounts();
  }

  private updateAmounts(): void {
    const amounts = this.paymentService.calculateAmounts(this.paymentData, this.totalAmount);
    this.remainingAmount = amounts.remaining;
    this.change = amounts.change;
  }

  handlePaymentMethodChange(method: string): void {
    if (!this.isProcessing) {
      this.paymentData.selectedPaymentMethod = method;
      this.isMixedPayment = method === 'mixed';

      if (this.isMixedPayment) {
        this.initializeMixedPayment();
      }

      this.updateAmounts();
    }
  }

  onAmountInput(input: HTMLInputElement, fieldKey: string): void {
    this.paymentData[fieldKey] = parseFloat(input.value) || 0;
    this.updateAmounts();
  }

  onConfirm(): void {
    if (this.isProcessing) return;

    if (this.paymentService.validatePayment(this.paymentData, this.customerName, this.customerPhone, this.totalAmount)) {
      const output: PaymentModalOutput = {
        paymentMethod: this.paymentData.selectedPaymentMethod,
        paymentData: this.paymentData,
        customerName: this.customerName,
        customerPhone: this.customerPhone
      };

      // Add mixed payment data if applicable
      if (this.isMixedPayment) {
        output.multiPaymentData = {
          cashAmount: this.cashAmountForMixed,
          onlineAmount: this.onlineAmountForMixed,
          onlineMethod: this.selectedOnlineMethod,
          upiId: this.selectedOnlineMethod === 'upi' ? this.paymentData.upiId : undefined,
          cardType: this.selectedOnlineMethod === 'card' ? this.paymentData.cardType : undefined
        };
      }

      this.confirm.emit(output);
    }
  }

  onCancel(): void {
    if (!this.isProcessing) {
      this.visible = false;
      this.visibleChange.emit(false);
      this.cancel.emit();
    }
  }

  private initializeMixedPayment(): void {
    this.cashAmountForMixed = 0;
    this.onlineAmountForMixed = this.totalAmount;
    this.selectedOnlineMethod = 'upi';
    this.updateMixedPaymentData();
  }

  private updateMixedPaymentData(): void {
    this.paymentData.cashAmount = this.cashAmountForMixed;
    this.paymentData['onlineAmount'] = this.onlineAmountForMixed;
    this.paymentData['onlineMethod'] = this.selectedOnlineMethod;
  }

  onCashAmountChange(amount: number): void {
    this.cashAmountForMixed = Math.max(0, Math.min(amount, this.totalAmount));
    this.onlineAmountForMixed = Math.max(0, this.totalAmount - this.cashAmountForMixed);
    this.updateMixedPaymentData();
    this.updateAmounts();
  }

  onOnlineMethodChange(method: 'upi' | 'card'): void {
    this.selectedOnlineMethod = method;
    this.updateMixedPaymentData();
  }

  // Utility method for template
  parseFloat(value: string): number {
    return parseFloat(value);
  }
}