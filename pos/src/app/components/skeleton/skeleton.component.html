<div class="w-full" *ngIf="form">
  <div class="w-full grid m-0">
    <div class="col-12 lg:col-4 md:col-6 p-0" *ngFor="let skeleton of [1,2,3,4,5,6,7,8,9]">
      <div class="general-conf field">
        <div class="flex justify-content-between">
          <label class="p-2 pb-0"> <p-skeleton width="10rem"></p-skeleton></label>
        </div>
        <div class="config-input p-2 pt-3">
          <p-skeleton width="100%" height="2rem"></p-skeleton>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="default-table p-3 pt-0" *ngIf="table">
  <p-table [value]="[1,2,3,4,5,6,7,8,9,10,11]" [styleClass]="'p-datatable-striped ' + styleClass" tableStyleClass="product-datatable">
    <ng-template pTemplate="header">
      <tr>
        <th scope="col" style="padding: 10px !important;" *ngFor="let number of [1,2,3,4,5,6]">
          <p-skeleton></p-skeleton>
        </th>
      </tr>
    </ng-template>
    <ng-template let-product pTemplate="body">
      <tr>
        <td style="padding: 10px !important;" *ngFor="let number of [1,2,3,4,5,6]">
          <p-skeleton></p-skeleton>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>
