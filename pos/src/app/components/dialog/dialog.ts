import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
@Component({
    selector: 'app-dialog',
    templateUrl: './dialog.html',
    imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    DialogModule,
]
})
export class DialogComponent {
    @Input() visible: boolean = false;
    @Output() visibleChange = new EventEmitter<boolean>();
    constructor() { }
}