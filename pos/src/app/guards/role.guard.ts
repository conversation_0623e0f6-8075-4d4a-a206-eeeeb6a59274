import { inject } from '@angular/core';
import { CanActivateFn, Router, UrlTree } from '@angular/router';
import { StorageService } from '../services/storage.service';
import { FirebaseAuthService } from '../services/firebase-auth.service';
import { Observable, of } from 'rxjs';
import { map, take, switchMap, catchError } from 'rxjs/operators';
import { User } from 'firebase/auth';

/**
 * Role-based guard to protect routes that require specific user roles
 * Usage: { path: 'admin', canActivate: [roleGuard(['admin'])] }
 */
export function roleGuard(allowedRoles: string[]): CanActivateFn {
  return (route, state): Observable<boolean | UrlTree> => {
    const router = inject(Router);
    const storageService = inject(StorageService);
    const authService = inject(FirebaseAuthService);
    
    // First check if the user is authenticated
    return authService.authState$.pipe(
      take(1),
      switchMap((firebaseUser: User | null) => {
        // Get user data with role information from storage
        const userData = storageService.getItem<any>('userFields');
        
        // If no Firebase user and no stored user data, redirect to login
        if (!firebaseUser && !userData) {
          console.log('Role Guard: User not authenticated - Redirecting to login');
          return of(router.createUrlTree(['/login']));
        }
        
        // Check if user has role information
        if (!userData || !userData.role) {
          console.log('Role Guard: Access denied - No role information');
          return of(router.createUrlTree(['/pages/home']));
        }
        
        // Check if user has one of the allowed roles
        const hasRole = allowedRoles.includes(userData.role);
        
        if (hasRole) {
          console.log(`Role Guard: Access granted - User has required role: ${userData.role}`);
          return of(true);
        } else {
          console.log(`Role Guard: Access denied - Required roles: ${allowedRoles.join(', ')}, User role: ${userData.role}`);
          return of(router.createUrlTree(['/pages/home']));
        }
      }),
      catchError(error => {
        console.error('Role Guard Error:', error);
        return of(router.createUrlTree(['/pages/home']));
      })
    );
  };
}
