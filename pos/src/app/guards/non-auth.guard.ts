import { inject } from '@angular/core';
import { CanActivateFn, Router, UrlTree } from '@angular/router';
import { FirebaseAuthService } from '../services/firebase-auth.service';
import { StorageService } from '../services/storage.service';
import { map, take, switchMap, catchError } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { User } from 'firebase/auth';

/**
 * Non-Auth guard to protect routes that should only be accessible when NOT authenticated
 * (like login page) - redirects to home if already authenticated
 */
export const nonAuthGuard: CanActivateFn = (route, state): Observable<boolean | UrlTree> => {
  const router = inject(Router);
  const authService = inject(FirebaseAuthService);
  const storageService = inject(StorageService);
  
  // Check Firebase auth state first
  return authService.authState$.pipe(
    take(1),
    switchMap((user: User | null) => {
      if (user) {
        // User is authenticated via Firebase, redirect to home
        console.log('Non-Auth Guard: User already authenticated via Firebase - Redirecting to home');
        return of(router.createUrlTree(['/pages/home']));
      }
      
      // If no Firebase user, check for stored token
      const token = storageService.getToken();
      if (token) {
        // User has a stored token, redirect to home
        console.log('Non-Auth Guard: User already authenticated via stored token - Redirecting to home');
        return of(router.createUrlTree(['/pages/home']));
      }
      
      // Not authenticated, allow access to non-auth routes
      console.log('Non-Auth Guard: Not authenticated - Access granted');
      return of(true);
    }),
    catchError(error => {
      console.error('Non-Auth Guard Error:', error);
      // On error, default to allowing access to non-auth routes
      return of(true);
    })
  );
};
