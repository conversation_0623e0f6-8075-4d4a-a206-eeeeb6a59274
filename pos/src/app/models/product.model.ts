/**
 * Base product interface with common properties
 */
export interface BaseProduct {
  id: string;
  name: string;
  description?: string;
  child_sku: string;
  ean_number?: string;
  selling_price: number;
  mrp?: number;
  discount_percentage?: number;
  thumbnail_image?: string;
  images?: string[];
  category_id?: string;
  category_name?: string;
  brand_id?: string;
  brand_name?: string;
  is_active: boolean;
  created_at: string | Date;
  updated_at: string | Date;
  quantity: number;
  tax: number;
  cgst: number;
  sgst: number;
  igst: number;
  cess: number;
  taxable: boolean;
}

/**
 * Product variant interface
 */
export interface ProductVariant {
  id: string;
  product_id: string;
  variant_name: string;
  child_sku: string;
  ean_number?: string;
  selling_price: number;
  mrp?: number;
  stock_quantity: number;
  attributes: ProductAttribute[];
  thumbnail_image?: string;
  images?: string[];
  is_active: boolean;
  tax: number;
  cgst: number;
  sgst: number;
  igst: number;
  cess: number;
  taxable: boolean;
}

/**
 * Product attribute interface (for variant attributes like size, color, etc.)
 */
export interface ProductAttribute {
  name: string;
  value: string;
}

/**
 * Extended product interface with variants
 */
export interface Product extends BaseProduct {
  variants?: ProductVariant[];
  stock_quantity: number;
}

/**
 * Cart item interface
 */
export interface CartItem extends Partial<Product> {
  id: string;
  child_sku: string;      
  selling_price: number;    
  quantity: number;
  tax: number;
  cgst: number;
  sgst: number;
  igst: number;
  cess: number;
  taxable: boolean;
}

/**
 * Product search result interface
 */
export interface ProductSearchResult {
  products: Product[];
  totalProducts: number;
}

/**
 * Product filter options interface
 */
export interface ProductFilterOptions {
  category_id?: string;
  brand_id?: string;
  price_min?: number;
  price_max?: number;
  sort_by?: 'price_asc' | 'price_desc' | 'name_asc' | 'name_desc' | 'newest';
  page?: number;
  limit?: number;
  search_term?: string;
}
