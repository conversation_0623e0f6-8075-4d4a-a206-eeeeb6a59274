/**
 * Order item interface
 */
export interface OrderItem {
  sku: string;
  child_sku?: string;
  name?: string;
  variant_name?: string;
  unit_price: number;
  sale_price: number;
  selling_price?: number; // Added for compatibility with existing code
  quantity: number;
  discount_amount?: number;
  total_price?: number;
  tax?: number;
  thumbnail_image?: string;
}

/**
 * Address interface for shipping and billing
 */
export interface Address {
  full_name: string;
  phone_number?: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  type_of_address: 'home' | 'work' | 'other';
}

/**
 * Payment details interface
 */
export interface PaymentDetails {
  payment_mode: 'cash' | 'card' | 'upi' | 'other';
  create_payment_order?: boolean;
}

/**
 * Order status type
 */
export type OrderStatus = 'pending' | 'processing' | 'completed' | 'cancelled' | 'refunded';

/**
 * Order interface
 */
export interface Order {
  order_id: string;
  customer_id: string;
  customer_name: string;
  facility_id: string;
  facility_name: string;
  order_date: string | Date;
  status: OrderStatus;
  total_amount: number;
  subtotal_amount: number;
  discount_amount?: number;
  tax_amount?: number;
  shipping_amount?: number;
  address: Address;
  payment?: PaymentDetails;
  items: OrderItem[];
  notes?: string;
  created_at: string | Date;
  updated_at: string | Date;
  total_items?: number 
}

/**
 * Order creation request interface
 */
export interface CreateOrderRequest {
  customer_id: string;
  customer_name: string;
  facility_id: string;
  facility_name: string;
  total_amount: number;
  address: Address;
  items: Omit<OrderItem, 'total_price'>[];
  payment?: PaymentDetails;
  notes?: string;
  is_approved?: boolean;
}

/**
 * Order filter options interface
 */
export interface OrderFilterOptions {
  customer_id?: string;
  status?: OrderStatus;
  date_from?: string | Date;
  date_to?: string | Date;
  min_amount?: number;
  max_amount?: number;
  page?: number;
  limit?: number;
}
