
export interface TaxConfig {
  isInclusive: boolean;  
  splitIgst: boolean;    
}

export interface PrintableItem {
  sku: string;
  name?: string;
  quantity: number;
  unit_price: number; 
  sale_price: number; 
  mrp: number;         
  discount: number;    
  total: number;
  taxableAmount?: number;
  cgstAmount?: number;
  sgstAmount?: number;
  igstAmount?: number;
  cessAmount?: number; 
  igst?: number;        
  cgst?: number;        
  sgst?: number;        
  cess?: number;        
  totalTax?: number;    
  totalWithTax?: number;
  created_at?: string;
}

export interface PrintTotals {
  totalTaxableAmount: number;
  totalCgstAmount: number;
  totalSgstAmount: number;
  totalIgstAmount: number;
  totalGstAmount?: number; 
  totalCessAmount: number;
  totalQuantity: number;
  totalTaxAmount: number;   
  processedItems?: PrintableItem[]; 
}

export interface PrintTemplateData {
  order_id: string;
  customer_name: string;
  customer_id: string;
  facility_name: string;
  total_amount: number;
  items: PrintableItem[];
  payment_method?: string;
  subtotal: number;
  discount: number;
  grand_total: number;
  copy_of_invoice?: boolean;
  currentDate: string;
  totals: PrintTotals;
  storeAddress: string;
  storePhone: string;
  storeGSTIN: string;
  storeEmail: string;
  created_at?: string;
}