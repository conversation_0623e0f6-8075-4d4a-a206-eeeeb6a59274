import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';
import { nonAuthGuard } from './guards/non-auth.guard';
import { roleGuard } from './guards/role.guard';
import { PagesComponent } from './pages/pages';
import { AdminComponent } from './admin/admin';
import { UsersComponent } from './admin/users/users';
import { FacilitiesComponent } from './admin/facilities/facilities';

export const routes: Routes = [
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.page').then((m) => m.LoginPage),
    canActivate: [nonAuthGuard]
  },
  {
    path: 'pages',
    component: PagesComponent,
    children: [
      {
        path: 'home',
        loadComponent: () => import('./pages/home/<USER>').then((m) => m.HomePage),
        canActivate: [authGuard]
      },
      {
        path: 'invoices',
        loadComponent: () => import('./pages/invoices/invoices').then((m) => m.InvoicesComponent),
        canActivate: [authGuard]
      }
    ],
    canActivate: [authGuard]
  },
  {
    path: 'admin',
    component: AdminComponent,
    children: [
      {
        path: 'users',
        component: UsersComponent,
        canActivate: [authGuard, roleGuard(['admin'])]
      },
      {
        path: 'facilities',
        component: FacilitiesComponent,
        canActivate: [authGuard, roleGuard(['admin'])]
      }
    ],
    canActivate: [authGuard, roleGuard(['admin'])]
  },
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
];
