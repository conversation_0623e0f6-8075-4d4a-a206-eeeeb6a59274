import { Injectable } from '@angular/core';
import { doc, getDoc, collection, getDocs, updateDoc, setDoc, deleteDoc } from 'firebase/firestore';
import { db } from '../firebase/firebase.config';
import { FirebaseAuthService } from '../services/firebase-auth.service';
import { StorageService } from '../services/storage.service';
import { CommonService } from '../services/common.service';
@Injectable({
  providedIn: 'root'
})
export class FirestoreService {
  userFields: any = {};
  constructor(
    private authService: FirebaseAuthService,
    private storage: StorageService,
    private common: CommonService
  ) {
  }
  
  /**
   * Get the current authenticated user
   * @returns Promise with the current user or null
   */
  public get getCurrentUser() {
    return this.authService.getCurrentUser();
  }


  async getFacilities(): Promise<any>{
    const user = await this.authService.getCurrentUser();
    if (!user) {
      console.log('No authenticated user found');
      this.common.setDisableBtn();
      return null;
    }
    const userId = user.uid;
    const facilitiesCollectionRef = collection(db, 'pos_users', userId, 'facilities');
    const facilitiesSnapshot = await getDocs(facilitiesCollectionRef);
    if (!facilitiesSnapshot.empty) {
      const facilitiesData = facilitiesSnapshot.docs.map(doc => {
        return {
          id: doc.id,
          ...doc.data()
        };
      });
      this.common.setDisableBtn();
      return facilitiesData;
    } else {
      this.common.setDisableBtn();
      return null;
    }
  }

  async getCurrentUserFields(): Promise<any>{
    const userFields = this.storage.getItem('userFields');
    if (userFields) {
      this.userFields = userFields;
      return userFields;
    }
    const user = await this.authService.getCurrentUser();
    if (!user) {
      console.log('No authenticated user found');
      this.common.setDisableBtn();
      return null;
    }
    const userId = String(user.phoneNumber)
    const userDocRef = doc(db, 'pos_users', userId);
    const userSnapshot = await getDoc(userDocRef);
    
    if (userSnapshot.exists()) {
      const data = userSnapshot.data()
      this.storage.setItem('userFields', data);
      this.userFields = data;
      this.common.setDisableBtn();
      return data
    } else {
      console.log('No user document found');
      this.common.setDisableBtn();
      return null;
    }
  }

  /**
   * Updates fields in a Firestore document
   * @param collectionPath - The collection path (e.g., 'pos_users')
   * @param docId - The document ID
   * @param data - Object containing the fields to update
   * @returns Promise that resolves when the update is complete
   */
  async updateFields(collectionPath: string, docId: string, data: Record<string, any>): Promise<void> {
    try {
      const docRef = doc(db, collectionPath, docId);
      const docSnapshot = await getDoc(docRef);
      if (docSnapshot.exists()) {
        const existingData = docSnapshot.data();
        const mergedData = { ...existingData, ...data };
        await updateDoc(docRef, mergedData);
        this.common.setDisableBtn();
      } else {
        await setDoc(docRef, data);
        this.common.setDisableBtn();
      }
    } catch (error) {
      this.common.setDisableBtn();
      throw error;
    }
  }

  /**
   * Updates fields in a nested document (subcollection)
   * @param parentCollection - The parent collection path (e.g., 'pos_users')
   * @param parentDocId - The parent document ID
   * @param subCollection - The subcollection name
   * @param subDocId - The subcollection document ID
   * @param data - Object containing the fields to update
   * @returns Promise that resolves when the update is complete
   */
  async updateNestedFields(
    parentCollection: string, parentDocId: string, 
    subCollection: string, subDocId: string, 
    data: Record<string, any>): Promise<void> {
    try {
      const docRef = doc(db, parentCollection, parentDocId, subCollection, subDocId);
      const docSnapshot = await getDoc(docRef);
      if (docSnapshot.exists()) {
        const existingData = docSnapshot.data();
        const mergedData = { ...existingData, ...data };
        await updateDoc(docRef, mergedData);
        this.common.setDisableBtn();
      } else {
        await setDoc(docRef, data);
        this.common.setDisableBtn();
      }
    } catch (error) {
      this.common.setDisableBtn();
      throw error;
    }
  }
}
