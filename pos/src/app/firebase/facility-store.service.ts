import { Injectable } from '@angular/core';
import { FirestoreService } from './firestore.service';
import { getDocs, collection, doc, getDoc, setDoc, updateDoc, deleteDoc, serverTimestamp, query, where } from 'firebase/firestore';
import { db } from '../firebase/firebase.config';
import { CommonService } from 'src/app/services/common.service';
@Injectable({
  providedIn: 'root'
})
export class FacilityStoreService {
  constructor(
    private firestoreService: FirestoreService,
    private common: CommonService
  ) { }

  async getAllFacilities(): Promise<any[]> {
    try {
      await this.firestoreService.getCurrentUserFields();
      const currentUserData = this.firestoreService.userFields;
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can access all facilities');
      }

      const facilitiesSnapshot = await getDocs(collection(db, 'facilities'));
      this.common.setDisableBtn();
      if (!facilitiesSnapshot.empty) {
        const facilitiesData = facilitiesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        return facilitiesData;
      } else {
        return [];
      }
    } catch (error) {
      this.common.setDisableBtn();
      console.error('Error getting facilities:', error);
      throw error;
    }
  }

  async getFacilityById(facilityId: string): Promise<any> {
    try {
      const facilityDoc = await getDoc(doc(db, 'facilities', facilityId));
      this.common.setDisableBtn();
      if (facilityDoc.exists()) {
        return {
          id: facilityDoc.id,
          ...facilityDoc.data()
        };
      } else {
        return null;
      }
    } catch (error) {
      this.common.setDisableBtn();
      console.error('Error getting facility:', error);
      throw error;
    }
  }

  async createFacility(facilityData: {
    facilityId: string;
    facilityCode: string;
    facilityName: string;
  }): Promise<any> {
    try {
      const currentUser = await this.firestoreService.getCurrentUser;
      await this.firestoreService.getCurrentUserFields();
      const currentUserData = this.firestoreService.userFields;
      
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can create facilities');
      }

      const facilitiesQuery = query(
        collection(db, 'facilities'),
        where('facilityCode', '==', facilityData.facilityCode)
      );
      
      const existingFacilities = await getDocs(facilitiesQuery);
      if (!existingFacilities.empty) {
        this.common.setDisableBtn();
        throw new Error(`Facility with code ${facilityData.facilityCode} already exists`);
      }

      const facilityId = facilityData.facilityId;
      const facilityRef = doc(db, 'facilities', facilityId);
      
      const existingFacilityDoc = await getDoc(facilityRef);
      if (existingFacilityDoc.exists()) {
        this.common.setDisableBtn();
        throw new Error(`Facility with ID ${facilityId} already exists`);
      }
      
      const newFacilityData = {
        facilityId,
        facilityCode: facilityData.facilityCode,
        facilityName: facilityData.facilityName,
        createdAt: serverTimestamp(),
        createdBy: currentUser?.uid
      };
      
      await setDoc(facilityRef, newFacilityData);
      this.common.setDisableBtn();
      return {
        id: facilityId,
        ...newFacilityData
      };
    } catch (error) {
      this.common.setDisableBtn();
      console.error('Error creating facility:', error);
      throw error;
    }
  }

  async updateFacility(facilityId: string, facilityData: {
    facilityCode?: string;
    facilityName?: string;
  }): Promise<boolean> {
    try {
      const currentUser = await this.firestoreService.getCurrentUser;
      await this.firestoreService.getCurrentUserFields();
      const currentUserData = this.firestoreService.userFields;
      
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can update facilities');
      }

      if (facilityData.facilityCode) {
        const facilitiesQuery = query(
          collection(db, 'facilities'),
          where('facilityCode', '==', facilityData.facilityCode)
        );
        
        const existingFacilities = await getDocs(facilitiesQuery);
        if (!existingFacilities.empty) {
          const existingFacility = existingFacilities.docs[0];
          if (existingFacility.id !== facilityId) {
            this.common.setDisableBtn();
            throw new Error(`Facility with code ${facilityData.facilityCode} already exists`);
          }
        }
      }

      const updateData = {
        ...facilityData,
        updatedAt: serverTimestamp(),
        updatedBy: currentUser?.uid
      };
      
      await updateDoc(doc(db, 'facilities', facilityId), updateData);
      this.common.setDisableBtn();
      return true;
    } catch (error) {
      this.common.setDisableBtn();
      console.error('Error updating facility:', error);
      throw error;
    }
  }

  async deleteFacility(facilityId: string): Promise<boolean> {
    try {
      await this.firestoreService.getCurrentUserFields();
      const currentUserData = this.firestoreService.userFields;
      
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can delete facilities');
      }

      await deleteDoc(doc(db, 'facilities', facilityId));
      this.common.setDisableBtn();
      return true;
    } catch (error) {
      this.common.setDisableBtn();
      console.error('Error deleting facility:', error);
      throw error;
    }
  }

  async assignFacilityToUser(userId: string, facilityId: string): Promise<boolean> {
    try {
      const currentUser = await this.firestoreService.getCurrentUser;
      await this.firestoreService.getCurrentUserFields();
      const currentUserData = this.firestoreService.userFields;
      
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can assign facilities to users');
      }

      const facilityDoc = await getDoc(doc(db, 'facilities', facilityId));
      if (!facilityDoc.exists()) {
        this.common.setDisableBtn();
        throw new Error(`Facility with ID ${facilityId} does not exist`);
      }

      const userDoc = await getDoc(doc(db, 'pos_users', userId));
      if (!userDoc.exists()) {
        this.common.setDisableBtn();
        throw new Error(`User with ID ${userId} does not exist`);
      }
      const facilityData = facilityDoc.data();
      const userFacilityData = {
        userId,
        facilityId,
        facilityCode: facilityData['facilityCode'],
        facilityName: facilityData['facilityName'],
        assignedAt: serverTimestamp(),
        assignedBy: currentUser?.uid
      };
      
      await setDoc(doc(db, 'pos_users', userId, 'facilities', facilityId), userFacilityData);
      this.common.setDisableBtn();
      return true;
    } catch (error) {
      this.common.setDisableBtn();
      console.error('Error assigning facility to user:', error);
      throw error;
    }
  }

  async removeFacilityFromUser(userId: string, facilityId: string): Promise<boolean> {
    try {
      await this.firestoreService.getCurrentUserFields();
      const currentUserData = this.firestoreService.userFields;
      
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can remove facilities from users');
      }

      await deleteDoc(doc(db, 'pos_users', userId, 'facilities', facilityId));
      this.common.setDisableBtn();
      return true;
    } catch (error) {
      this.common.setDisableBtn();
      console.error('Error removing facility from user:', error);
      throw error;
    }
  }
}