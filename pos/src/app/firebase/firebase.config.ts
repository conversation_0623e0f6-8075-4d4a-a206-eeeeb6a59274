import { initializeApp } from 'firebase/app';
import { getAuth, setPersistence, browserLocalPersistence } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';

// Firebase configuration
export const firebaseConfig = {
  apiKey: "AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g",
  authDomain: "rozana-app-pos-prod-fcce5.firebaseapp.com",
  projectId: "rozana-app-pos-prod-fcce5",
  storageBucket: "rozana-app-pos-prod-fcce5.firebasestorage.app",
  messagingSenderId: "1063678246919",
  appId: "1:1063678246919:web:582f955c33f74de799eaa2",
  measurementId: "G-Q2V64T3DZS"
};

const app = initializeApp(firebaseConfig);

export const auth = getAuth(app);

export const db = getFirestore(app);
try {
  setPersistence(auth, browserLocalPersistence)
    .then(() => console.log('Firebase persistence set to LOCAL'))
    .catch(error => console.error('Error setting persistence:', error));
} catch (error) {
  console.error('Error setting Firebase persistence:', error);
}

console.log('Firestore initialized successfully');
