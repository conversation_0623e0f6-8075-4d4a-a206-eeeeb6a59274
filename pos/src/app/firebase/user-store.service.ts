import { Injectable } from '@angular/core';
import { FirestoreService } from './firestore.service';
import { getDocs, collection, deleteDoc, doc, updateDoc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/firebase.config';
import { CommonService } from '../services/common.service';
@Injectable({
  providedIn: 'root'
})
export class UserStoreService {
  constructor(
    private firestoreService: FirestoreService,
    private common: CommonService
  ) { }

  async getAllUsers() {
    try {
      // Query the users collection
      const usersSnapshot = await getDocs(collection(db, 'pos_users'));
      
      // Map the documents to a more usable format
      const users = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      this.common.setDisableBtn();
      return users;
    } catch (error) {
      this.common.setDisableBtn();
      console.error("Error fetching users:", error);
      throw error;
    }
  }
  
  async createUser(formData: any) {
    try {
      const currentUser = await this.firestoreService.getCurrentUser;
      const currentUserData = await this.firestoreService.getCurrentUserFields();
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can create new users');
      }
      
      const userId = '+91' + formData.phoneNumber;
      const userData = {
        phoneNumber: userId,
        displayName: formData.displayName,
        role: formData.role,
        facilities: formData.facilities,
        createdAt: new Date(),
        createdBy: currentUser?.uid,
        updatedAt: new Date(),
        updatedBy: currentUser?.uid
      };
      
      await setDoc(doc(db, 'pos_users', userId), userData);
      this.common.setDisableBtn();
      return {
        id: userId,
        ...userData
      };
    } catch (error) {
      this.common.setDisableBtn();
      console.error("Error creating user:", error);
      throw error;
    }
  }
  
  async updateUser(userId: string, userData: any = {}) {
    try {
      const currentUser = await this.firestoreService.getCurrentUser;
      const currentUserData = await this.firestoreService.getCurrentUserFields();
      
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can update user roles');
      }
      const updatedUserId = userId?.includes('+91') ? userId : '+91' + userId;
      const updateData = {
        ...userData,
        phoneNumber: updatedUserId,
        updatedAt: new Date(),
        updatedBy: currentUser?.uid
      };
      
      await updateDoc(doc(db, 'pos_users', updatedUserId), updateData);
      this.common.setDisableBtn();
      return true;
    } catch (error) {
      this.common.setDisableBtn();
      console.error("Error updating user:", error);
      throw error;
    }
  }
  
  async deleteUser(userId: string) {
    try {
      const currentUserData = await this.firestoreService.getCurrentUserFields();
      if (currentUserData?.role !== 'admin') {
        this.common.setDisableBtn();
        throw new Error('Only admins can delete users');
      }
      await deleteDoc(doc(db, 'pos_users', userId));
      this.common.setDisableBtn();
      return true;
    } catch (error) {
      this.common.setDisableBtn();
      console.error("Error deleting user:", error);
      throw error;
    }
  }
  
  async getUserById(userId: string) {
    try {
      const currentUserData = await this.firestoreService.getCurrentUserFields();
      
      if (currentUserData?.role !== 'admin' && userId !== currentUserData?.uid) {
        this.common.setDisableBtn();
        throw new Error('Permission denied');
      }
      
      const updatedUserId = userId?.includes('+91') ? userId : '+91' + userId;
      const userDoc = await getDoc(doc(db, 'pos_users', updatedUserId));
      this.common.setDisableBtn();
      if (userDoc.exists()) {
        return {
          id: userDoc.id,
          ...userDoc.data()
        };
      }
      return null;
    } catch (error) {
      this.common.setDisableBtn();
      console.error("Error getting user:", error);
      throw error;
    }
  }
}