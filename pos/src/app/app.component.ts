import { Component } from '@angular/core';
import { IonApp, IonMenu, IonRouterOutlet, MenuController } from '@ionic/angular/standalone';
import { ToastModule } from 'primeng/toast';
import { MenuModule } from 'primeng/menu';
import { Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { filter, takeUntil } from 'rxjs/operators';
import { FirebaseAuthService } from './services/firebase-auth.service';
import { StorageService } from './services/storage.service';
import { Subject } from 'rxjs';
import { FirestoreService } from './firebase/firestore.service';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService } from 'primeng/api';
@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  imports: [
    IonApp,
    IonMenu,
    IonRouterOutlet,
    ToastModule,
    MenuModule,
    CommonModule,
    ConfirmDialogModule,
  ],
  providers: [ConfirmationService]
})
export class AppComponent {
  items: any[] = [];

  showMenu = true;
  isAuthenticated = false;
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router, 
    private menuController: MenuController,
    private authService: FirebaseAuthService,
    private storageService: StorageService,
    private firestoreService: FirestoreService
  ) {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe((event: any) => {
      this.showMenu = !event.url.includes('/login');
    });
    this.authService.authState$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.isAuthenticated = !!user || !!this.storageService.getToken();
      this.prepareMenu();
    });
  }
  
  async prepareMenu(){
    const userFields: any = await this.firestoreService.getCurrentUserFields();
    let adminOptions: any[] = []
    if (userFields?.role === 'admin') {
      adminOptions = [
        { label: 'Users', icon: 'fi fi-rr-user', command: () => this.onMenuClick('/admin/users')},
        { label: 'Facilities', icon: 'fi fi-rr-building', command: () => this.onMenuClick('/admin/facilities')},
      ]
    }
    this.items = [
      { label: 'Billing', icon: 'fi fi-rr-shopping-cart', command: () => this.onMenuClick('/pages/home') },
      { label: 'Invoices', icon: 'fi fi-rr-file-invoice-dollar', command: () => this.onMenuClick('/pages/invoices')},
      ...adminOptions,
      { label: 'Logout', icon: 'fi fi-rr-sign-out-alt', command: () => this.logout() },
    ]
  }

  onMenuClick(url:string){
    this.router.navigateByUrl(url);
    this.menuController.close();
  }
  logout(){
    this.menuController.close();
    this.authService.signOut();
  }
  
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
